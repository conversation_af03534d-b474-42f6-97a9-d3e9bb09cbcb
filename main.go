package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/modelcontextprotocol/go-sdk/mcp"
)

// MCP 客戶端配置常量
const (
	// 伺服器連接配置
	MCPServerURL = "https://csai_uat_deepface.chainsea.com.tw/mcp/"
	APIKeyHeader = "CS-API-Key"
	APIKeyValue  = "NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU"

	// 連接參數
	ConnectionTimeout = 60 * time.Second
	RequestTimeout    = 30 * time.Second
)

// MCPClientConfig MCP 客戶端配置結構
type MCPClientConfig struct {
	ServerURL    string            // 伺服器 URL
	Headers      map[string]string // 請求標頭
	Timeout      time.Duration     // 連接超時
	AutoApprove  []string          // 自動批准列表
	Disabled     bool              // 是否禁用
}

// MCPClient MCP 客戶端結構
type MCPClient struct {
	config     *MCPClientConfig
	client     *mcp.Client
	session    *mcp.ClientSession
	httpClient *http.Client
}

// NewMCPClient 創建新的 MCP 客戶端實例
func NewMCPClient() *MCPClient {
	// 創建 HTTP 客戶端配置
	httpClient := &http.Client{
		Timeout: RequestTimeout,
	}

	// 創建 MCP 客戶端配置
	config := &MCPClientConfig{
		ServerURL: MCPServerURL,
		Headers: map[string]string{
			APIKeyHeader:    APIKeyValue,
			"Content-Type":  "application/json",
			"Accept":        "text/event-stream",
		},
		Timeout:     ConnectionTimeout,
		AutoApprove: []string{}, // 空陣列
		Disabled:    false,      // enabled 狀態
	}

	// 創建 MCP 客戶端實現
	impl := &mcp.Implementation{
		Name:    "mcp-test-client",
		Version: "v1.0.0",
	}

	// 創建 MCP 客戶端
	client := mcp.NewClient(impl, nil)

	return &MCPClient{
		config:     config,
		client:     client,
		httpClient: httpClient,
	}
}

// Initialize 初始化 MCP 連接並進行握手
func (c *MCPClient) Initialize(ctx context.Context) error {
	g.Log().Info(ctx, "Initializing MCP client connection...")

	// 創建自定義 SSE 傳輸層，包含認證標頭
	transport := &mcp.SSEClientTransport{
		Endpoint:   c.config.ServerURL,
		HTTPClient: c.createAuthenticatedHTTPClient(),
	}

	// 建立連接 - 使用正確的 Connect 方法簽名
	session, err := c.client.Connect(ctx, transport, nil)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to connect to MCP server: %v", err)
		return fmt.Errorf("failed to connect to MCP server: %w", err)
	}

	c.session = session
	g.Log().Info(ctx, "Successfully connected to MCP server")

	// MCP 協議中沒有單獨的 Initialize 方法，連接成功即表示初始化完成
	// 我們可以通過 Ping 來測試連接
	err = c.session.Ping(ctx, nil)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to ping MCP server: %v", err)
		return fmt.Errorf("failed to ping MCP server: %w", err)
	}

	g.Log().Info(ctx, "MCP connection and ping successful")
	return nil
}

// createAuthenticatedHTTPClient 創建包含認證標頭的 HTTP 客戶端
func (c *MCPClient) createAuthenticatedHTTPClient() *http.Client {
	// 創建自定義 RoundTripper 來添加認證標頭
	transport := &http.Transport{}

	client := &http.Client{
		Timeout:   c.config.Timeout,
		Transport: &authenticatedTransport{
			base:    transport,
			headers: c.config.Headers,
		},
	}

	return client
}

// authenticatedTransport 自定義傳輸層，用於添加認證標頭
type authenticatedTransport struct {
	base    http.RoundTripper
	headers map[string]string
}

// RoundTrip 實現 http.RoundTripper 接口
func (t *authenticatedTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	// 複製請求以避免修改原始請求
	newReq := req.Clone(req.Context())

	// 添加認證標頭
	for key, value := range t.headers {
		newReq.Header.Set(key, value)
	}
	 t.base.
	return t.base.RoundTrip(newReq)
}

// ListTools 獲取伺服器可用工具列表
func (c *MCPClient) ListTools(ctx context.Context) (*mcp.ListToolsResult, error) {
	if c.session == nil {
		return nil, fmt.Errorf("MCP session not initialized, please call Initialize() first")
	}

	g.Log().Info(ctx, "Requesting tools list from MCP server...")

	// 調用 ListTools API
	result, err := c.session.ListTools(ctx, &mcp.ListToolsParams{})
	if err != nil {
		g.Log().Errorf(ctx, "Failed to list tools: %v", err)
		return nil, fmt.Errorf("failed to list tools: %w", err)
	}

	g.Log().Infof(ctx, "Successfully retrieved %d tools from server", len(result.Tools))

	// 記錄每個工具的詳細信息
	for i, tool := range result.Tools {
		g.Log().Infof(ctx, "Tool %d: Name=%s, Description=%s",
			i+1, tool.Name, tool.Description)

		// 如果有輸入模式，記錄模式信息
		if tool.InputSchema != nil {
			g.Log().Debugf(ctx, "Tool %s input schema type: %v",
				tool.Name, tool.InputSchema.Type)
		}
	}

	return result, nil
}

// Close 關閉 MCP 連接
func (c *MCPClient) Close() error {
	if c.session != nil {
		g.Log().Info(context.Background(), "Closing MCP session...")
		err := c.session.Close()
		if err != nil {
			g.Log().Errorf(context.Background(), "Error closing MCP session: %v", err)
			return err
		}
		c.session = nil
		g.Log().Info(context.Background(), "MCP session closed successfully")
	}
	return nil
}

// IsConnected 檢查是否已連接
func (c *MCPClient) IsConnected() bool {
	return c.session != nil
}

// MCPError 自定義 MCP 錯誤類型
type MCPError struct {
	Operation string // 操作名稱
	Cause     error  // 原始錯誤
	Message   string // 錯誤訊息
}

// Error 實現 error 接口
func (e *MCPError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("MCP %s failed: %s (cause: %v)", e.Operation, e.Message, e.Cause)
	}
	return fmt.Sprintf("MCP %s failed: %s", e.Operation, e.Message)
}

// Unwrap 支援錯誤鏈
func (e *MCPError) Unwrap() error {
	return e.Cause
}

// NewMCPError 創建新的 MCP 錯誤
func NewMCPError(operation, message string, cause error) *MCPError {
	return &MCPError{
		Operation: operation,
		Message:   message,
		Cause:     cause,
	}
}

// validateConfig 驗證客戶端配置
func (c *MCPClient) validateConfig() error {
	if c.config == nil {
		return NewMCPError("validation", "client configuration is nil", nil)
	}

	if c.config.ServerURL == "" {
		return NewMCPError("validation", "server URL cannot be empty", nil)
	}

	if c.config.Timeout <= 0 {
		return NewMCPError("validation", "timeout must be positive", nil)
	}

	// 驗證 API Key
	if apiKey, exists := c.config.Headers[APIKeyHeader]; !exists || apiKey == "" {
		return NewMCPError("validation", "API key is required", nil)
	}

	return nil
}

// handleConnectionError 處理連接錯誤
func (c *MCPClient) handleConnectionError(ctx context.Context, err error) error {
	g.Log().Errorf(ctx, "Connection error occurred: %v", err)

	// 清理會話
	if c.session != nil {
		c.session.Close()
		c.session = nil
	}

	return NewMCPError("connection", "failed to establish or maintain connection", err)
}

// retryWithBackoff 帶退避的重試機制
func (c *MCPClient) retryWithBackoff(ctx context.Context, operation string, maxRetries int, fn func() error) error {
	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		err := fn()
		if err == nil {
			if attempt > 1 {
				g.Log().Infof(ctx, "Operation %s succeeded on attempt %d", operation, attempt)
			}
			return nil
		}

		lastErr = err
		g.Log().Warningf(ctx, "Operation %s failed on attempt %d/%d: %v",
			operation, attempt, maxRetries, err)

		if attempt < maxRetries {
			// 指數退避：1s, 2s, 4s, 8s...
			backoffDuration := time.Duration(1<<(attempt-1)) * time.Second
			g.Log().Infof(ctx, "Retrying %s in %v...", operation, backoffDuration)

			select {
			case <-ctx.Done():
				return NewMCPError(operation, "operation cancelled", ctx.Err())
			case <-time.After(backoffDuration):
				// 繼續重試
			}
		}
	}

	return NewMCPError(operation, fmt.Sprintf("failed after %d attempts", maxRetries), lastErr)
}

// runMCPTest 執行 MCP 客戶端測試
func runMCPTest(ctx context.Context) error {
	g.Log().Info(ctx, "Starting MCP client test...")

	// 創建 MCP 客戶端
	client := NewMCPClient()
	defer func() {
		if err := client.Close(); err != nil {
			g.Log().Errorf(ctx, "Error closing client: %v", err)
		}
	}()

	// 驗證配置
	if err := client.validateConfig(); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}
	g.Log().Info(ctx, "Client configuration validated successfully")

	// 使用重試機制初始化連接
	err := client.retryWithBackoff(ctx, "initialize", 3, func() error {
		return client.Initialize(ctx)
	})
	if err != nil {
		return fmt.Errorf("failed to initialize MCP client: %w", err)
	}

	// 檢查連接狀態
	if !client.IsConnected() {
		return fmt.Errorf("client is not connected after initialization")
	}
	g.Log().Info(ctx, "MCP client connected and ready")

	// 獲取工具列表
	g.Log().Info(ctx, "Fetching available tools...")
	toolsResult, err := client.ListTools(ctx)
	if err != nil {
		return fmt.Errorf("failed to list tools: %w", err)
	}

	// 顯示工具列表詳細信息
	g.Log().Infof(ctx, "=== Available Tools Summary ===")
	if len(toolsResult.Tools) == 0 {
		g.Log().Info(ctx, "No tools available on the server")
	} else {
		for i, tool := range toolsResult.Tools {
			g.Log().Infof(ctx, "Tool %d:", i+1)
			g.Log().Infof(ctx, "  Name: %s", tool.Name)
			g.Log().Infof(ctx, "  Description: %s", tool.Description)

			if tool.InputSchema != nil {
				g.Log().Infof(ctx, "  Input Schema Type: %v", tool.InputSchema.Type)
				if tool.InputSchema.Properties != nil {
					g.Log().Infof(ctx, "  Properties: %d defined", len(tool.InputSchema.Properties))
				}
			}
			g.Log().Info(ctx, "  ---")
		}
	}

	g.Log().Info(ctx, "MCP client test completed successfully")
	return nil
}

func main() {
	// 設置日誌配置
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	// 創建帶超時的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()

	g.Log().Info(ctx, "=== MCP Client Test Application ===")
	g.Log().Infof(ctx, "Server URL: %s", MCPServerURL)
	g.Log().Infof(ctx, "Connection Timeout: %v", ConnectionTimeout)
	g.Log().Infof(ctx, "Request Timeout: %v", RequestTimeout)

	// 執行測試
	if err := runMCPTest(ctx); err != nil {
		g.Log().Errorf(ctx, "Test failed: %v", err)
		log.Fatalf("MCP client test failed: %v", err)
	}

	g.Log().Info(ctx, "All tests passed successfully!")
}
